import { NextRequest, NextResponse } from 'next/server';
import { sessionService } from '@/services/sessionService';
import { clientService } from '@/services/clientService';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const clientName = searchParams.get('clientName');
    const sessionId = searchParams.get('sessionId');

    if (sessionId) {
      // Get specific session by ID
      const session = await sessionService.getById(sessionId);
      if (!session) {
        return NextResponse.json({ error: 'Session not found' }, { status: 404 });
      }

      const client = session.clientId ? await clientService.getById(session.clientId) : null;

      return NextResponse.json({
        session: {
          id: session.id,
          clientId: session.clientId,
          sessionType: session.sessionType,
          bookingDate: session.bookingDate,
          bookingTime: session.bookingTime,
          eventId: session.eventId,
          hasEventId: !!session.eventId,
          notes: session.notes,
          quote: session.quote
        },
        client: client ? {
          id: client.id,
          firstName: client.firstName,
          lastName: client.lastName,
          email: client.email,
          dogName: client.dogName
        } : null
      });
    }

    // Get all sessions or filter by client name
    const sessions = await sessionService.getAll();
    const clients = await clientService.getAll();

    // Create a map of client ID to client data
    const clientMap = new Map();
    clients.forEach(client => {
      clientMap.set(client.id, client);
    });

    let filteredSessions = sessions;

    if (clientName) {
      // Filter sessions by client name (case insensitive)
      const lowerClientName = clientName.toLowerCase();
      filteredSessions = sessions.filter(session => {
        if (!session.clientId) return false;
        const client = clientMap.get(session.clientId);
        if (!client) return false;
        const fullName = `${client.firstName} ${client.lastName}`.toLowerCase();
        return fullName.includes(lowerClientName) || 
               client.firstName?.toLowerCase().includes(lowerClientName) ||
               client.lastName?.toLowerCase().includes(lowerClientName);
      });
    }

    // Format the response with session and client data
    const sessionData = filteredSessions.map(session => {
      const client = session.clientId ? clientMap.get(session.clientId) : null;
      return {
        session: {
          id: session.id,
          clientId: session.clientId,
          sessionType: session.sessionType,
          bookingDate: session.bookingDate,
          bookingTime: session.bookingTime,
          eventId: session.eventId,
          hasEventId: !!session.eventId,
          notes: session.notes,
          quote: session.quote
        },
        client: client ? {
          id: client.id,
          firstName: client.firstName,
          lastName: client.lastName,
          email: client.email,
          dogName: client.dogName,
          fullName: `${client.firstName} ${client.lastName}`.trim()
        } : null
      };
    });

    // Sort by booking date (most recent first)
    sessionData.sort((a, b) => {
      const dateA = new Date(`${a.session.bookingDate}T${a.session.bookingTime}`);
      const dateB = new Date(`${b.session.bookingDate}T${b.session.bookingTime}`);
      return dateB.getTime() - dateA.getTime();
    });

    return NextResponse.json({
      message: `Found ${sessionData.length} sessions${clientName ? ` for client name containing "${clientName}"` : ''}`,
      totalSessions: sessions.length,
      filteredSessions: sessionData.length,
      sessionsWithEventId: sessionData.filter(s => s.session.hasEventId).length,
      sessionsWithoutEventId: sessionData.filter(s => !s.session.hasEventId).length,
      sessions: sessionData
    });

  } catch (error) {
    console.error('Error debugging sessions:', error);
    return NextResponse.json({
      error: 'Failed to debug sessions',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
