import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  const testSessionUpdateData = {
    sessionId: "test-session-id-12345",
    clientId: "test-client-id-67890",
    clientFirstName: "Louise",
    clientEmail: "<EMAIL>",
    dogName: "Test Dog",
    sessionType: "In-Person",
    bookingDate: "2025-07-12",
    bookingTime: "14:00",
    notes: "Test session update",
    quote: 75.00,
    eventId: "test-event-id-abcdef",
    hasSignedBookingTerms: true,
    hasFilledQuestionnaire: true,
    updatedAt: new Date().toISOString(),
    isUpdate: true
  };

  try {
    console.log('Testing session update webhook with data:', testSessionUpdateData);

    // Call the Make.com session update webhook
    const response = await fetch('https://hook.eu1.make.com/yaoalfe77uqtw4xv9fbh5atf4okq14wm', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testSessionUpdateData)
    });

    const responseText = await response.text();
    let result;
    try {
      result = JSON.parse(responseText);
    } catch {
      result = { message: responseText };
    }

    console.log('Make.com webhook response:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
      response: result
    });

    return NextResponse.json({
      message: 'Test session update webhook call completed',
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
      testData: testSessionUpdateData,
      makeResponse: result
    });

  } catch (error) {
    console.error('Error testing session update webhook:', error);
    return NextResponse.json({
      error: 'Failed to test session update webhook',
      message: error instanceof Error ? error.message : 'Unknown error',
      testData: testSessionUpdateData
    }, { status: 500 });
  }
}
